import React, { useEffect, useState, useCallback } from 'react';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import { Row, Col, Card } from 'react-bootstrap';
import API from '../services/API';
import { PERMITS_ANALYTICS, PERMITS_DETAILED_RESULTS } from '../constants';
import AllPermit from '../pages/AllPermits';
import WaterChart from './WaterChart';
import BarOne from './BarOne';
import HighRiskActivityLineChart from './HighRiskActivityLineChart';
import BreakdownActivityLineChart from './BreakdownActivityLineChart';
import { getDisplayDateRange } from './dateUtils';

/**
 * EPTW Dashboard Component
 *
 * PERFORMANCE OPTIMIZATIONS IMPLEMENTED:
 * 1. Single consolidated API endpoint: /permits/analytics
 * 2. All dashboard data fetched in one request
 * 3. Debounced API calls: Prevents rapid successive calls on dateRange changes
 * 4. Separate detailed results API: Only fetches when user clicks on cards
 * 5. Data passed to child components: Eliminates individual API calls in children
 * 6. Loading states: Clear feedback during data loading
 */
function Eptw({ dateRange, filterCriteria }) {

   
    const displayDateRange = getDisplayDateRange(dateRange);
    const [type, setType] = useState('');
    const [detailedData, setDetailedData] = useState([]);
    const [loadingAnalytics, setLoadingAnalytics] = useState(true);
    const [loadingDetails, setLoadingDetails] = useState(false);

    // All analytics data from single API call
    const [analyticsData, setAnalyticsData] = useState({
        statistics: null,
        typeDistribution: null,
        typeDistributionByMonth: null,
        riskProfile: null,
        highRiskBreakdown: null
    });

    // Load analytics data from consolidated endpoint
    const loadAnalyticsData = useCallback(async () => {
        setLoadingAnalytics(true);
        try {
            const payload = {};

            // Add date range if available
            if (dateRange && dateRange.length === 2) {
                const [from, to] = dateRange.map((date) => new Date(date).toISOString());
                payload.from = from;
                payload.to = to;
            }

            // Add filter criteria names if available
            if (filterCriteria && filterCriteria.length > 0) {
                const projectNames = filterCriteria.map(item => item.name);
                payload.projects = projectNames.join(',');
            }

            const response = await API.post(PERMITS_ANALYTICS, payload);
            if (response.status === 200) {
                setAnalyticsData(response.data);
            }
        } catch (error) {
            console.error("Error loading permits analytics data:", error);
            // Set empty data on error to prevent crashes
            setAnalyticsData({
                statistics: null,
                typeDistribution: null,
                typeDistributionByMonth: null,
                riskProfile: null,
                highRiskBreakdown: null
            });
        } finally {
            setLoadingAnalytics(false);
        }
    }, [dateRange, filterCriteria]);

    // Load detailed results when user clicks on cards
    const loadDetailedResults = useCallback(async (resultType) => {
        setLoadingDetails(true);
        try {
            const typeMapping = {
                1: 'firstTimeApproved',
                2: 'revoked'
            };

            const payload = {
                type: typeMapping[resultType]
            };

            // Add date range if available
            if (dateRange && dateRange.length === 2) {
                const [from, to] = dateRange.map((date) => new Date(date).toISOString());
                payload.from = from;
                payload.to = to;
            }

            // Add filter criteria names if available
            if (filterCriteria && filterCriteria.length > 0) {
                const projectNames = filterCriteria.map(item => item.name);
                payload.projects = projectNames.join(',');
            }

            const response = await API.post(PERMITS_DETAILED_RESULTS, payload);
            if (response.status === 200) {
                setDetailedData(response.data.data || []);
            }
        } catch (error) {
            console.error("Error fetching detailed results:", error);
            setDetailedData([]);
        } finally {
            setLoadingDetails(false);
        }
    }, [dateRange, filterCriteria]);

    useEffect(() => {
        loadAnalyticsData();
    }, [loadAnalyticsData]);

    // Debounced reload when dateRange changes
    useEffect(() => {
        if (dateRange && dateRange.length === 2) {
            const debounceTimer = setTimeout(() => {
                loadAnalyticsData();
            }, 300);

            return () => clearTimeout(debounceTimer);
        }
    }, [dateRange, loadAnalyticsData]);

    // Reload analytics data when filterCriteria changes
    useEffect(() => {
        if (filterCriteria) {
            const debounceTimer = setTimeout(() => {
                loadAnalyticsData();
            }, 300);

            return () => clearTimeout(debounceTimer);
        }
    }, [filterCriteria, loadAnalyticsData]);

    const handleCardClick = (selectedType) => {
        const newType = type === selectedType ? '' : selectedType;
        setType(newType);

        // Load detailed data when card is selected
        if (newType && (newType === 1 || newType === 2)) {
            loadDetailedResults(newType);
        } else {
            setDetailedData([]);
        }
    };

    // Helper component for loading state
    const ChartLoadingState = () => (
        <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '200px',
            color: '#666'
        }}>
            <div>Loading chart data...</div>
        </div>
    );

    return (
        <>
            <Row className="mb-4">
                <Col md={3}>
                    <Card
                        className={`cursor-pointer ${type === 1 ? 'selected-card' : ''} ${loadingAnalytics ? 'opacity-50' : ''}`}
                        onClick={() => !loadingAnalytics && handleCardClick(1)}
                    >
                        <Card.Body className="d-flex flex-column">
                            <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                                <div>
                                    <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                        {loadingAnalytics ? '...' : (analyticsData.statistics?.firstTimeApprovalRate || 'N/A')}
                                    </div>
                                    <div>First time approval rate of permit applications</div>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>

                <Col md={3}>
                    <Card
                        className={`cursor-pointer ${type === 2 ? 'selected-card' : ''} ${loadingAnalytics ? 'opacity-50' : ''}`}
                        onClick={() => !loadingAnalytics && handleCardClick(2)}
                    >
                        <Card.Body className="d-flex flex-column">
                            <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                                <div>
                                    <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                        {loadingAnalytics ? '...' : (analyticsData.statistics?.revokedCount || 'N/A')}
                                    </div>
                                    <div>Permits revoked due to violation of identified controls</div>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>

                <Col md={3}>
                    <Card
                        className={`cursor-pointer ${type === 3 ? 'selected-card' : ''} ${loadingAnalytics ? 'opacity-50' : ''}`}
                        onClick={() => !loadingAnalytics && handleCardClick(3)}
                    >
                        <Card.Body className="d-flex flex-column">
                            <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                                <div>
                                    <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                        0
                                    </div>
                                    <div>No. of at-risk observations in work activities under permit</div>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>

                <Col md={3}>
                    <Card
                        className={`cursor-pointer ${type === 4 ? 'selected-card' : ''} ${loadingAnalytics ? 'opacity-50' : ''}`}
                        onClick={() => !loadingAnalytics && handleCardClick(4)}
                    >
                        <Card.Body className="d-flex flex-column">
                            <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                                <div>
                                    <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                        0
                                    </div>
                                    <div>No. of safety incidents in work activities under permit</div>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col md="12">
                    {type !== '' && (
                        loadingDetails ? (
                            <div style={{ textAlign: 'center', padding: '20px' }}>
                                <div>Loading detailed results...</div>
                            </div>
                        ) : (
                            <AllPermit
                                data={detailedData}
                                from="dash"
                            />
                        )
                    )}
                </Col>
            </Row>

            <Row className="mb-4">
                <Col md={6}>
                    <Card className="">
                        <Card.Body>
                            <h5 className='font-weight-bold'>Breakdown of Permit Types | {displayDateRange}</h5>
                            {loadingAnalytics ? <ChartLoadingState /> : <WaterChart type={'eptw'} data={analyticsData.typeDistribution} />}
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card className="">
                        <Card.Body>
                            <h5 className="font-weight-bold">Risk Profile of ePermit-to-Work Activities | {displayDateRange}</h5>
                            {loadingAnalytics ? <ChartLoadingState /> : <HighRiskActivityLineChart dateRange={dateRange} data={analyticsData.riskProfile} />}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mb-4">
                <Col md={12}>
                    <Card className="mb-4">
                        <Card.Body>
                            <h5 className='font-weight-bold'>Rolling Data Trend of Permit Applications | {displayDateRange} </h5>
                            {loadingAnalytics ? <ChartLoadingState /> : <BarOne type={'eptw'} dateRange={dateRange} data={analyticsData.typeDistributionByMonth} />}
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={12}>
                    <Card className="">
                        <Card.Body>
                            <h5 className="font-weight-bold">High-Risk Activity Breakdown | {displayDateRange}</h5>
                            {loadingAnalytics ? <ChartLoadingState /> : <BreakdownActivityLineChart dateRange={dateRange} data={analyticsData.highRiskBreakdown} />}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </>
    );
}

export default Eptw;
